export default function RetrievalIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 20 20"
      width="20"
      height="20"
      style={{ width: '100%', height: '100%', transform: 'translate3d(0px, 0px, 0px)' }}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        <clipPath id="__lottie_element_258">
          <rect width="20" height="20" x="0" y="0" />
        </clipPath>
        <clipPath id="__lottie_element_263">
          <path d="M0,0 L20000,0 L20000,20000 L0,20000z" />
        </clipPath>
        <clipPath id="__lottie_element_270">
          <path d="M0,0 L20000,0 L20000,20000 L0,20000z" />
        </clipPath>
      </defs>
      <g clipPath="url(#__lottie_element_258)">
        <g
          clipPath="url(#__lottie_element_263)"
          style={{ display: 'block' }}
          transform="matrix(0.9999999403953552,0,0,0.9999999403953552,-10000,-10000)"
          opacity="1"
        >
          <g
            clipPath="url(#__lottie_element_270)"
            style={{ display: 'block' }}
            transform="matrix(0.9999988675117493,0,0,0.9999988675117493,10.01171875,10.01171875)"
            opacity="1"
          >
            <g style={{ display: 'block' }} transform="matrix(1,0,0,1,10000,10000)" opacity="1">
              <g opacity="1" transform="matrix(1,0,0,1,-3.25,-2.125)">
                <path
                  fill="rgb(177,97,253)"
                  fillOpacity="1"
                  d=" M0,0.75 C0,0.3357900083065033 0.3357900083065033,8.881784197001252e-16 0.75,8.881784197001252e-16 C2.4166667461395264,8.881784197001252e-16 4.083333492279053,8.881784197001252e-16 5.75,8.881784197001252e-16 C6.1641998291015625,8.881784197001252e-16 6.5,0.3357900083065033 6.5,0.75 C6.5,1.1642099618911743 6.1641998291015625,1.5 5.75,1.5 C4.083333492279053,1.5 2.4166667461395264,1.5 0.75,1.5 C0.3357900083065033,1.5 0,1.1642099618911743 0,0.75 C0,0.75 0,0.75 0,0.75 C0,0.75 0,0.75 0,0.75"
                />
              </g>
            </g>
            <g style={{ display: 'block' }} transform="matrix(1,0,0,1,10000,10000)" opacity="1">
              <g opacity="1" transform="matrix(1,0,0,1,-3.25,0.625)">
                <path
                  fill="rgb(177,97,253)"
                  fillOpacity="1"
                  d=" M0,0.75 C0,0.3357999920845032 0.3357900083065033,0 0.75,0 C1.9166666269302368,0 3.0833332538604736,0 4.25,0 C4.6641998291015625,0 5,0.3357999920845032 5,0.75 C5,1.164199948310852 4.6641998291015625,1.5 4.25,1.5 C3.0833332538604736,1.5 1.9166666269302368,1.5 0.75,1.5 C0.3357900083065033,1.5 0,1.164199948310852 0,0.75 C0,0.75 0,0.75 0,0.75 C0,0.75 0,0.75 0,0.75"
                />
              </g>
            </g>
          </g>
          <g style={{ display: 'block' }} transform="matrix(1,0,0,1,10000,10000)" opacity="1">
            <g opacity="1" transform="matrix(1,0,0,1,10,10)">
              <path
                fill="rgb(177,97,253)"
                fillOpacity="1"
                d=" M-3.13,-5.25 C-3.09,-5.25 -3.04,-5.25 -3,-5.25 C-2.59,-5.25 -2.25,-4.91 -2.25,-4.5 C-2.25,-4.09 -2.59,-3.75 -3,-3.75 C-3.03,-3.75 -3.07,-3.75 -3.1,-3.75 C-3.53,-3.75 -3.81,-3.75 -4.02,-3.73 C-4.23,-3.72 -4.3,-3.69 -4.34,-3.67 C-4.48,-3.6 -4.6,-3.48 -4.67,-3.34 C-4.69,-3.3 -4.72,-3.23 -4.73,-3.02 C-4.75,-2.81 -4.75,-2.53 -4.75,-2.1 C-4.75,-2.07 -4.75,-2.03 -4.75,-2 C-4.75,-1.59 -5.09,-1.25 -5.5,-1.25 C-5.91,-1.25 -6.25,-1.59 -6.25,-2 C-6.25,-2.04 -6.25,-2.09 -6.25,-2.13 C-6.25,-2.52 -6.25,-2.87 -6.23,-3.15 C-6.2,-3.44 -6.15,-3.74 -6,-4.02 C-5.79,-4.44 -5.44,-4.79 -5.02,-5 C-4.74,-5.15 -4.44,-5.2 -4.15,-5.23 C-3.87,-5.25 -3.52,-5.25 -3.13,-5.25 C-3.13,-5.25 -3.13,-5.25 -3.13,-5.25 C-3.13,-5.25 -3.13,-5.25 -3.13,-5.25 M6.25,-2.13 C6.25,-2.09 6.25,-2.04 6.25,-2 C6.25,-1.59 5.91,-1.25 5.5,-1.25 C5.09,-1.25 4.75,-1.59 4.75,-2 C4.75,-2.03 4.75,-2.07 4.75,-2.1 C4.75,-2.53 4.75,-2.81 4.73,-3.02 C4.72,-3.23 4.69,-3.3 4.67,-3.34 C4.6,-3.48 4.48,-3.6 4.34,-3.67 C4.3,-3.69 4.23,-3.72 4.02,-3.73 C3.81,-3.75 3.53,-3.75 3.1,-3.75 C3.07,-3.75 3.03,-3.75 3,-3.75 C2.59,-3.75 2.25,-4.09 2.25,-4.5 C2.25,-4.91 2.59,-5.25 3,-5.25 C3.04,-5.25 3.09,-5.25 3.13,-5.25 C3.52,-5.25 3.87,-5.25 4.15,-5.23 C4.44,-5.2 4.74,-5.15 5.02,-5 C5.44,-4.79 5.79,-4.44 6,-4.02 C6.15,-3.74 6.2,-3.44 6.23,-3.15 C6.25,-2.87 6.25,-2.52 6.25,-2.13 C6.25,-2.13 6.25,-2.13 6.25,-2.13 C6.25,-2.13 6.25,-2.13 6.25,-2.13 M-3.13,5.25 C-3.09,5.25 -3.04,5.25 -3,5.25 C-2.59,5.25 -2.25,4.91 -2.25,4.5 C-2.25,4.09 -2.59,3.75 -3,3.75 C-3.03,3.75 -3.07,3.75 -3.1,3.75 C-3.53,3.75 -3.81,3.75 -4.02,3.73 C-4.23,3.72 -4.3,3.69 -4.34,3.67 C-4.48,3.6 -4.6,3.48 -4.67,3.34 C-4.69,3.3 -4.72,3.23 -4.73,3.02 C-4.75,2.81 -4.75,2.53 -4.75,2.1 C-4.75,2.07 -4.75,2.03 -4.75,2 C-4.75,1.59 -5.09,1.25 -5.5,1.25 C-5.91,1.25 -6.25,1.59 -6.25,2 C-6.25,2.04 -6.25,2.09 -6.25,2.13 C-6.25,2.52 -6.25,2.87 -6.23,3.15 C-6.2,3.44 -6.15,3.74 -6,4.02 C-5.79,4.44 -5.44,4.79 -5.02,5 C-4.74,5.15 -4.44,5.2 -4.15,5.23 C-3.87,5.25 -3.52,5.25 -3.13,5.25 C-3.13,5.25 -3.13,5.25 -3.13,5.25 C-3.13,5.25 -3.13,5.25 -3.13,5.25 M6.25,2.13 C6.25,2.09 6.25,2.04 6.25,2 C6.25,1.59 5.91,1.25 5.5,1.25 C5.09,1.25 4.75,1.59 4.75,2 C4.75,2.03 4.75,2.07 4.75,2.1 C4.75,2.53 4.75,2.81 4.73,3.02 C4.72,3.23 4.69,3.3 4.67,3.34 C4.6,3.48 4.48,3.6 4.34,3.67 C4.3,3.69 4.23,3.72 4.02,3.73 C3.81,3.75 3.53,3.75 3.1,3.75 C3.07,3.75 3.03,3.75 3,3.75 C2.59,3.75 2.25,4.09 2.25,4.5 C2.25,4.91 2.59,5.25 3,5.25 C3.04,5.25 3.09,5.25 3.13,5.25 C3.52,5.25 3.87,5.25 4.15,5.23 C4.44,5.2 4.74,5.15 5.02,5 C5.44,4.79 5.79,4.44 6,4.02 C6.15,3.74 6.2,3.44 6.23,3.15 C6.25,2.87 6.25,2.52 6.25,2.13 C6.25,2.13 6.25,2.13 6.25,2.13 C6.25,2.13 6.25,2.13 6.25,2.13"
              />
              <g opacity="1" transform="matrix(1,0,0,1,-6.250000476837158,-5.250000476837158)">
                <path fill="rgb(177,97,253)" fillOpacity="1" d="M0 0" />
              </g>
              <g opacity="1" transform="matrix(1,0,0,1,2.25,-5.250000476837158)">
                <path fill="rgb(177,97,253)" fillOpacity="1" d="M0 0" />
              </g>
              <g opacity="1" transform="matrix(1,0,0,1,-6.250000476837158,1.25)">
                <path fill="rgb(177,97,253)" fillOpacity="1" d="M0 0" />
              </g>
              <g opacity="1" transform="matrix(1,0,0,1,2.25,1.25)">
                <path fill="rgb(177,97,253)" fillOpacity="1" d="M0 0" />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}
