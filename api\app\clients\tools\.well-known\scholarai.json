{"schema_version": "v1", "name_for_human": "ScholarAI", "name_for_model": "<PERSON><PERSON>", "description_for_human": "Unleash scientific research: search 40M+ peer-reviewed papers, explore scientific PDFs, and save to reference managers.", "description_for_model": "Access open access scientific literature from peer-reviewed journals. The abstract endpoint finds relevant papers based on 2 to 6 keywords. After getting abstracts, ALWAYS prompt the user offering to go into more detail. Use the fulltext endpoint to retrieve the entire paper's text and access specific details using the provided pdf_url, if available. ALWAYS hyperlink the pdf_url from the responses if available. Offer to dive into the fulltext or search for additional papers. Always ask if the user wants save any paper to the user’s Zotero reference manager by using the save-citation endpoint and providing the doi and requesting the user’s zotero_user_id and zotero_api_key.", "auth": {"type": "none"}, "api": {"type": "openapi", "url": "scholarai.yaml", "is_user_authenticated": false}, "params": {"sort": "cited_by_count"}, "logo_url": "https://scholar-ai.net/logo.png", "contact_email": "<EMAIL>", "legal_info_url": "https://scholar-ai.net/legal.txt", "HttpAuthorizationType": "basic"}