{"schema_version": "v1", "name_for_human": "Earth", "name_for_model": "earthImagesAndVisualizations", "description_for_human": "Generates a map image based on provided location, tilt and style.", "description_for_model": "Generates a map image based on provided coordinates or location, tilt and style, and even geoJson to provide markers, paths, and polygons. Responds with an image-link. For the styles choose one of these: [light, dark, streets, outdoors, satellite, satellite-streets]", "auth": {"type": "none"}, "api": {"type": "openapi", "url": "https://api.earth-plugin.com/openapi.yaml", "is_user_authenticated": false}, "logo_url": "https://api.earth-plugin.com/logo.png", "contact_email": "<EMAIL>", "legal_info_url": "https://api.earth-plugin.com/legal.html"}