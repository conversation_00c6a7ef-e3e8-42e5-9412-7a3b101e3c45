import { cn } from '~/utils/';

export default function Plugin({ className = '', ...props }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="none"
      className={cn('h-4 w-4', className)}
      width="16"
      height="16"
      strokeWidth="2"
      {...props}
    >
      <g fill="currentColor">
        <path d="M13.164.98a.7.7 0 0 0-1.328 0l-.478 1.435a.7.7 0 0 1-.443.443l-1.436.478a.7.7 0 0 0 0 1.328l1.436.479a.7.7 0 0 1 .443.442l.478 1.436a.7.7 0 0 0 1.328 0l.478-1.436a.7.7 0 0 1 .443-.443l1.436-.478a.7.7 0 0 0 0-1.328l-1.436-.478a.7.7 0 0 1-.443-.443L13.164.979Z" />
        <path d="M13.237 10.534c-.228-.245-.513-.46-.847-.46a.823.823 0 0 0-.828.849c.04 1.04.128 2.067.263 3.08a.619.619 0 0 1-.528.695c-.872.121-1.748.208-2.626.262a.8.8 0 0 1-.845-.805c0-.325.21-.602.45-.82.235-.215.375-.488.375-.787 0-.683-.738-1.237-1.65-1.237-.911 0-1.65.554-1.65 1.237 0 .294.137.563.364.775.245.229.461.513.461.848a.823.823 0 0 1-.85.829 33.809 33.809 0 0 1-3.266-.278.619.619 0 0 1-.532-.532 34.099 34.099 0 0 1-.278-3.267.823.823 0 0 1 .83-.85c.333 0 .619.216.846.461.212.228.482.364.776.364.683 0 1.237-.738 1.237-1.65 0-.91-.554-1.65-1.237-1.65-.299 0-.572.142-.786.376-.219.24-.496.45-.821.45a.8.8 0 0 1-.805-.845c.054-.885.142-1.76.262-2.626a.619.619 0 0 1 .695-.528c1.022.136 2.05.224 3.08.263a.822.822 0 0 0 .85-.828c0-.334-.217-.62-.462-.847-.227-.212-.363-.482-.363-.776C5.352 1.554 6.09 1 7.002 1c.91 0 1.649.554 1.649 1.237 0 .173-.012.327-.029.473C8.258 3 8 3.41 8 4c0 1.5 1.667 1.833 2.5 2 .167.833.5 2.5 2 2.5.732 0 1.186-.397 1.479-.9l.034-.001c.683 0 1.237.738 1.237 1.65 0 .911-.554 1.65-1.237 1.65-.294 0-.564-.137-.776-.364Z" />
      </g>
    </svg>
  );
}
