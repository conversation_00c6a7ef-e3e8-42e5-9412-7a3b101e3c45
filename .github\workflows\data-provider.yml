name: Node.js Package

on:
  push:
    branches:
      - main
    paths:
      - 'packages/data-provider/package.json'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 16
      - run: cd packages/data-provider && npm ci
      - run: cd packages/data-provider && npm run build

  publish-npm:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 16
          registry-url: 'https://registry.npmjs.org'
      - run: cd packages/data-provider && npm ci
      - run: cd packages/data-provider && npm run build
      - run: cd packages/data-provider && npm publish
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
