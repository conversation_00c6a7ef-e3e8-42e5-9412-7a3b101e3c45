{"schema_version": "v1", "name_for_human": "Scholarly Graph Link", "name_for_model": "scholarly_graph_link", "description_for_human": "You can search papers, authors, datasets and software. It has access to Figshare, Arxiv, and many others.", "description_for_model": "Run GraphQL queries against an API hosted by DataCite API. The API supports most GraphQL query but does not support mutations statements. Use `{ __schema { types { name kind } } }` to get all the types in the GraphQL schema. Use `{ datasets { nodes { id sizes citations { nodes { id titles { title } } } } } }` to get all the citations of all datasets in the API. Use `{ datasets { nodes { id sizes citations { nodes { id titles { title } } } } } }` to get all the citations of all datasets in the API. Use `{person(id:ORCID) {works(first:50) {nodes {id titles(first: 1){title} publicationYear}}}}` to get the first 50 works of a person based on their ORCID. All Ids are urls, e.g., https://orcid.org/0012-0000-1012-1110. Mutations statements are not allowed.", "auth": {"type": "none"}, "api": {"type": "openapi", "url": "https://api.datacite.org/graphql-openapi.yaml", "is_user_authenticated": false}, "logo_url": "https://raw.githubusercontent.com/kjgarza/scholarly_graph_link/master/logo.png", "contact_email": "<EMAIL>", "legal_info_url": "https://github.com/kjgarza/scholarly_graph_link/blob/master/LICENSE"}