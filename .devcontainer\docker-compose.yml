services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    # restart: always
    links:
      - mongodb
      - meilisearch
    # ports:
    #   - 3080:3080               # Change it to 9000:3080 to use nginx
    extra_hosts: # if you are running APIs on docker you need access to, you will need to uncomment this line and next
    - "host.docker.internal:host-gateway"

    volumes:
      # This is where VS Code should expect to find your project's source code and the value of "workspaceFolder" in .devcontainer/devcontainer.json
      - ..:/workspaces:cached
      # Uncomment the next line to use Docker from inside the container. See https://aka.ms/vscode-remote/samples/docker-from-docker-compose for details.
      # - /var/run/docker.sock:/var/run/docker.sock 
    environment:
      - HOST=0.0.0.0
      - MONGO_URI=mongodb://mongodb:27017/LibreChat
      # - CHATGPT_REVERSE_PROXY=http://host.docker.internal:8080/api/conversation # if you are hosting your own chatgpt reverse proxy with docker
      # - OPENAI_REVERSE_PROXY=http://host.docker.internal:8070/v1/chat/completions # if you are hosting your own chatgpt reverse proxy with docker
      - MEILI_HOST=http://meilisearch:7700

    # Runs app on the same network as the service container, allows "forwardPorts" in devcontainer.json function.
    # network_mode: service:another-service
    
    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally. 
    # (Adding the "ports" property to this file will not forward from a Codespace.)

    # Use a non-root user for all processes - See https://aka.ms/vscode-remote/containers/non-root for details.
    user: vscode

    # Overrides default command so things don't shut down after the process ends.
    command: /bin/sh -c "while sleep 1000; do :; done"  

  mongodb:
    container_name: chat-mongodb
    expose:
      - 27017
    # ports:
    #   - 27018:27017
    image: mongo
    # restart: always
    volumes:
      - ./data-node:/data/db
    command: mongod --noauth
  meilisearch:
    container_name: chat-meilisearch
    image: getmeili/meilisearch:v1.5
    # restart: always
    expose:
      - 7700
    # Uncomment this to access meilisearch from outside docker
    # ports: 
    #   - 7700:7700 # if exposing these ports, make sure your master key is not the default value
    environment:
      - MEILI_NO_ANALYTICS=true
      - MEILI_MASTER_KEY=5c71cf56d672d009e36070b5bc5e47b743535ae55c818ae3b735bb6ebfb4ba63
    volumes:
      - ./meili_data_v1.5:/meili_data
