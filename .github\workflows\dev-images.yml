name: Docker Dev Images Build

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'api/**'
      - 'client/**'
      - 'packages/**'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - target: api-build
            file: Dockerfile.multi
            image_name: librechat-dev-api
          - target: node
            file: Dockerfile
            image_name: librechat-dev

    steps:
      # Check out the repository
      - name: Checkout
        uses: actions/checkout@v4

      # Set up QEMU
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Log in to GitHub Container Registry
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Login to Docker Hub
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # Prepare the environment
      - name: Prepare environment
        run: |
          cp .env.example .env

      # Build and push Docker images for each target
      - name: Build and push Docker images
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.file }}
          push: true
          tags: |
            ghcr.io/${{ github.repository_owner }}/${{ matrix.image_name }}:${{ github.sha }}
            ghcr.io/${{ github.repository_owner }}/${{ matrix.image_name }}:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/${{ matrix.image_name }}:${{ github.sha }}
            ${{ secrets.DOCKERHUB_USERNAME }}/${{ matrix.image_name }}:latest
          platforms: linux/amd64,linux/arm64
          target: ${{ matrix.target }}
