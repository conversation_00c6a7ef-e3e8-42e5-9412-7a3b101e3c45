name: Deploy_GH<PERSON>unner_Linux_ACI

on:
  workflow_dispatch:

env:
  RUNNER_VERSION: 2.293.0
  ACI_RESOURCE_GROUP: 'Demo-ACI-GitHub-Runners-RG'
  ACI_NAME: 'gh-runner-linux-01'
  DNS_NAME_LABEL: 'gh-lin-01'
  GH_OWNER: ${{ github.repository_owner }}
  GH_REPOSITORY: 'LibreChat' #Change here to deploy self hosted runner ACI to another repo.

jobs:
  deploy-gh-runner-aci:
    runs-on: ubuntu-latest
    steps:
      # checkout the repo
      - name: 'Checkout GitHub Action'
        uses: actions/checkout@v4

      - name: 'Login via Azure CLI'
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: 'Deploy to Azure Container Instances'
        uses: 'azure/aci-deploy@v1'
        with:
          resource-group: ${{ env.ACI_RESOURCE_GROUP }}
          image: ${{ secrets.REGISTRY_LOGIN_SERVER }}/pwd9000-github-runner-lin:${{ env.RUNNER_VERSION }}
          registry-login-server: ${{ secrets.REGISTRY_LOGIN_SERVER }}
          registry-username: ${{ secrets.REGISTRY_USERNAME }}
          registry-password: ${{ secrets.REGISTRY_PASSWORD }}
          name: ${{ env.ACI_NAME }}
          dns-name-label: ${{ env.DNS_NAME_LABEL }}
          environment-variables: GH_TOKEN=${{ secrets.PAT_TOKEN }} GH_OWNER=${{ env.GH_OWNER }} GH_REPOSITORY=${{ env.GH_REPOSITORY }}
          location: 'eastus'
